import request from '@/utils/request'
import type {
  SelectCourseParams,
  SelectCourseResponse,
  SelectCourseListQuery,
  SelectCourseListResponse,
  SchoolSelectCourseQuery,
  SchoolSelectCourseResponse,
  UnselectCourseParams,
  UnselectCourseResponse,
  AvailableCourseQuery,
  AvailableCourseResponse,
  SelectedCoursesQuery,
  SelectedCoursesResponse,
  SelectCourseSettingResponse,
  SelectCourseStatisticsResponse,
  SelectCourseScoreQuery,
  SelectCourseScoreResponse,
  RebuildCourseQuery,
  RebuildCourseResponse,
  RestudySelectParams,
  RestudySelectResponse,
} from '@/types/selectCourse'

/**
 * 更新选课信息
 * @param params 选课参数
 * @returns 更新结果
 */
export function updateSelectCourse(params: SelectCourseParams): Promise<SelectCourseResponse> {
  return request('/teacher/selectCourse/update', {
    method: 'POST',
    data: params,
  })
}

/**
 * 新增选课信息
 * @param params 选课参数
 * @returns 创建结果
 */
export function createSelectCourse(params: SelectCourseParams): Promise<SelectCourseResponse> {
  return request('/teacher/selectCourse/add', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取选课列表
 * @param params 查询参数
 * @returns 选课列表
 */
export function getSelectCourseList(
  data: SelectCourseListQuery,
): Promise<SelectCourseListResponse> {
  return request('/teacher/selectCourse/list', {
    method: 'POST',
    data,
  })
}

/**
 * 获取本学期课程列表
 * @param data 查询参数
 * @returns 本学期课程列表
 */
export function getCurrentSemesterCourses(
  data: SelectCourseListQuery,
): Promise<SelectCourseListResponse> {
  return request('/teacher/selectCourse/list', {
    method: 'POST',
    data,
  })
}

/**
 * 获取学生选课列表
 * @param params 查询参数
 * @returns 学生选课列表结果
 */
export function getSchoolSelectCourseList(
  params: SchoolSelectCourseQuery,
): Promise<SchoolSelectCourseResponse> {
  return request('/student/selectCourse/schoolSelect', {
    method: 'POST',
    data: params,
  })
}

/**
 * 退选课程
 * @param params 退选参数
 * @returns 退选结果
 */
export function unselectCourse(params: UnselectCourseParams): Promise<UnselectCourseResponse> {
  return request('/student/selectCourse/schoolSelect', {
    method: 'POST',
    data: params,
  })
}

// 获取可选课程列表
export function getAvailableCourseList(
  params: AvailableCourseQuery,
): Promise<AvailableCourseResponse> {
  const pj = JSON.stringify(params)
  const pjo: AvailableCourseQuery = JSON.parse(pj)
  pjo.page = undefined
  pjo.pageSize = undefined
  return request('/student/selectCourse/schoolSelect', {
    method: 'POST',
    data: pjo,
  })
}

// 选课
export function selectCourse(params: SelectCourseParams): Promise<SelectCourseResponse> {
  return request('/student/selectCourse/schoolSelect', {
    method: 'POST',
    data: params,
  })
}

// 获取选课详情
export function getSelectCourseInfo(selectCourseId: number): Promise<any> {
  return request('/teacher/selectCourse/info', {
    method: 'POST',
    data: { selectCourseId },
  })
}

// 获取已选课程列表
export function getSelectedCourses(params: SelectedCoursesQuery): Promise<SelectedCoursesResponse> {
  return request('/student/selectCourse', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取学生选课设置信息
 * @returns 选课设置信息
 */
export function getSelectCourseSetting(): Promise<SelectCourseSettingResponse> {
  return request('/student/selectCourse/selectCourseSet', {
    method: 'POST',
    data: {},
  })
}

/**
 * 获取教师选课统计信息
 * @returns 选课统计信息
 */
export function getSelectCourseStatistics(): Promise<SelectCourseStatisticsResponse> {
  return request('/teacher/selectCourse/statistics', {
    method: 'POST',
    data: {},
  })
}

/**
 * 获取学生选课成绩信息
 * @param params 查询参数
 * @returns 学生选课成绩列表
 */
export function getSelectCourseScores(
  params: SelectCourseScoreQuery,
): Promise<SelectCourseScoreResponse> {
  return request('/student/selectCourse/schoolSelect', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取学生选课重建列表
 * @param params 查询参数
 * @returns 选课重建列表
 */
export function getRebuildCourseList(params: RebuildCourseQuery): Promise<RebuildCourseResponse> {
  return request('/student/selectCourse/rebuild', {
    method: 'POST',
    data: params,
  })
}

/**
 * 重修选课确认
 * @param params 重修选课参数
 * @returns 重修选课确认结果
 */
export function confirmRestudySelect(params: RestudySelectParams): Promise<RestudySelectResponse> {
  return request('/student/selectCourse/schoolSelect', {
    method: 'POST',
    data: params,
  })
}
