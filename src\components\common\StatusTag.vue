<template>
  <view
    v-if="text"
    class="flex items-center rounded-[12rpx]"
    :class="[statusClass, sizeClass, { 'border border-solid': showBorder }]"
  >
    <!-- 可选的图标 -->
    <wd-icon v-if="showIcon && iconName" :name="iconName" :size="String(iconSize)" />
    <text :class="{ 'ml-[4rpx]': showIcon && iconName }">{{ text }}</text>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'

/**
 * 状态标识组件
 * 用于显示各种状态的标签，支持多种预定义样式
 */

// 状态类型定义 - 使用通用类型
export type StatusType =
  | 'primary' // 主要状态 - 蓝色
  | 'success' // 成功状态 - 绿色
  | 'warning' // 警告状态 - 橙色
  | 'danger' // 危险状态 - 红色
  | 'error' // 错误状态 - 红色
  | 'info' // 信息状态 - 蓝色
  | 'purple' // 紫色状态
  | 'default' // 默认状态 - 灰色

// 尺寸类型定义
export type StatusSize = 'mini' | 'small' | 'normal' | 'large'

// 组件属性
interface Props {
  /** 显示的文本内容 */
  text: string
  /** 状态类型 */
  type: StatusType
  /** 标签尺寸 */
  size?: StatusSize
  /** 是否显示图标 */
  showIcon?: boolean
  /** 图标名称 */
  iconName?: string
  /** 图标大小 */
  iconSize?: string | number
  /** 是否显示边框 */
  showBorder?: boolean
}

// 定义props，设置默认值
const props = withDefaults(defineProps<Props>(), {
  size: 'normal',
  showIcon: false,
  iconName: '',
  iconSize: 14,
  showBorder: true,
})

// 计算状态样式类
const statusClass = computed(() => {
  // 基础样式（文字颜色和背景色）
  const baseClasses: Record<StatusType, string> = {
    primary: 'text-blue-600 bg-blue-50',
    success: 'text-green-600 bg-green-50',
    warning: 'text-orange-600 bg-orange-50',
    danger: 'text-red-600 bg-red-50',
    error: 'text-red-600 bg-red-50',
    info: 'text-blue-600 bg-blue-50',
    purple: 'text-purple-600 bg-purple-50',
    default: 'text-gray-600 bg-gray-50',
  }

  // 边框颜色样式
  const borderClasses: Record<StatusType, string> = {
    primary: 'border-blue-200',
    success: 'border-green-200',
    warning: 'border-orange-200',
    danger: 'border-red-200',
    error: 'border-red-200',
    info: 'border-blue-200',
    purple: 'border-purple-300 ',
    default: 'border-gray-200',
  }

  const baseClass = baseClasses[props.type] || baseClasses.default
  const borderClass = props.showBorder ? borderClasses[props.type] || borderClasses.default : ''

  return `${baseClass} ${borderClass}`.trim()
})

// 计算尺寸样式类
const sizeClass = computed(() => {
  const sizeClasses: Record<StatusSize, string> = {
    mini: 'p-[4rpx] px-[8rpx] text-[20rpx]',
    small: 'p-[6rpx] px-[12rpx] text-[22rpx]',
    normal: 'p-[8rpx] px-[16rpx] text-[24rpx]',
    large: 'p-[10rpx] px-[20rpx] text-[26rpx]',
  }

  return sizeClasses[props.size] || sizeClasses.normal
})
</script>

<style lang="scss" scoped>
// 组件样式已通过UnoCSS类实现，无需额外样式
</style>
