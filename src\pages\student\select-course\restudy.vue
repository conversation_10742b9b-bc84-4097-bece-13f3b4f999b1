<route lang="json5">
{
  style: {
    navigationBarTitleText: '重新选课操作',
  },
}
</route>
<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useUserStore } from '@/store/user'
import { getRebuildCourseList, confirmRestudySelect } from '@/service/selectCourse'
import { useMessage } from 'wot-design-uni'
import Pagination from '@/components/Pagination/index.vue'
import StatusTag from '@/components/common/StatusTag.vue'
import ConfirmDialog from '@/components/common/ConfirmDialog.vue'
import ActionButton from '@/components/common/ActionButton.vue'
import OptionSelector from '@/components/common/OptionSelector.vue'
import type {
  RebuildCourseItem,
  RebuildCourseQuery,
  CourseDetailInfo,
  RestudySelectParams,
} from '@/types/selectCourse'

// 初始化消息框组件
const message = useMessage()

// 用户信息
const userStore = useUserStore()
const userInfo = computed(() => ({
  name: userStore.userInfo.realname,
  major: userStore.userInfo.department,
  grade: userStore.userInfo.className,
}))

// 课程总数
const total = ref(0)

// 搜索相关
const searchType = ref('courseName') // 默认搜索课程名称
const searchValue = ref('') // 搜索关键词
const searchOptions = [
  { value: 'courseName', label: '课程名称' },
  { value: 'leaderTeacherName', label: '教师姓名' },
  { value: 'className', label: '教学班' },
]

// 处理搜索类型变更
const handleSearchTypeChange = (value: string) => {
  searchType.value = value
}

// 处理搜索
const handleSearch = () => {
  // 重置页码
  queryParams.value.page = 1
  // 清空之前的搜索条件
  queryParams.value.courseName = ''
  queryParams.value.leaderTeacherName = ''
  queryParams.value.className = ''

  // 设置当前搜索条件
  if (searchType.value === 'courseName') {
    queryParams.value.courseName = searchValue.value
  } else if (searchType.value === 'leaderTeacherName') {
    queryParams.value.leaderTeacherName = searchValue.value
  } else if (searchType.value === 'className') {
    queryParams.value.className = searchValue.value
  }

  // 重新获取数据
  fetchRebuildCourses()
}

// 清空搜索
const clearSearch = () => {
  searchValue.value = ''
  queryParams.value.courseName = ''
  queryParams.value.leaderTeacherName = ''
  queryParams.value.className = ''
  fetchRebuildCourses()
}

// 加载状态
const loading = ref(false)

// 查询参数
const queryParams = ref<RebuildCourseQuery>({
  page: 1,
  pageSize: 10,
  semesters: ['2024-2025|1'], // 默认当前学期
  courseName: '',
  leaderTeacherName: '',
  className: '',
  courseTotalHours: '',
  weekHours: '',
  creditHour: '',
  startWeek: '',
  teachingInfo: '',
  siteName: '',
  maxCount: '',
  selectedCount: '',
  campusName: '',
})

// 重建课程列表
const rebuildCourses = ref<RebuildCourseItem[]>([])

// 获取重建课程数据
const fetchRebuildCourses = async () => {
  loading.value = true
  try {
    const res = await getRebuildCourseList(queryParams.value)
    rebuildCourses.value = res.items || []
    total.value = res.total || 0
  } catch (error) {
    console.error('获取重建课程列表失败:', error)
    uni.showToast({
      title: '获取课程列表失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 处理页面变化
const handlePageChange = (newPage: number) => {
  queryParams.value.page = newPage
  fetchRebuildCourses()
}

// 重修选课弹窗相关状态
const restudyDialogRef = ref<InstanceType<typeof ConfirmDialog> | null>(null)
const currentCourse = ref<RebuildCourseItem | null>(null)
const selectedRelatedCourse = ref<CourseDetailInfo | null>(null)
const selectedCourseIndex = ref(-1)
const submitLoading = ref(false)

// 计算关联课程选项
const courseOptions = computed(() => {
  if (!currentCourse.value || !currentCourse.value.kcxx) {
    return []
  }
  return currentCourse.value.kcxx.map((course) => {
    return `${course.xn} (${course.xq}) ${course.kcmc} ${course.cj}`
  })
})

// 计算选中课程的显示文本
const selectedCourseText = computed(() => {
  if (selectedCourseIndex.value >= 0 && courseOptions.value.length > 0) {
    return courseOptions.value[selectedCourseIndex.value]
  }
  return ''
})

// 打开重修选课弹窗
const openRestudyDialog = (course: RebuildCourseItem) => {
  currentCourse.value = course
  selectedRelatedCourse.value = null
  selectedCourseIndex.value = -1
  restudyDialogRef.value?.show()
}

// 关闭重修选课弹窗
const closeRestudyDialog = () => {
  restudyDialogRef.value?.hide()
  currentCourse.value = null
  selectedRelatedCourse.value = null
  selectedCourseIndex.value = -1
}

// 处理关联课程选择
const handleCourseOptionChange = (event: any) => {
  const index = event.detail.value
  selectedCourseIndex.value = index
  if (currentCourse.value && currentCourse.value.kcxx && index >= 0) {
    selectedRelatedCourse.value = currentCourse.value.kcxx[index]
  } else {
    selectedRelatedCourse.value = null
  }
}

// 提交重修选课申请
const submitRestudyApplication = async () => {
  if (!currentCourse.value || !selectedRelatedCourse.value || submitLoading.value) {
    uni.showToast({
      title: '请先选择关联课程',
      icon: 'none',
    })
    return
  }

  submitLoading.value = true
  try {
    // 调用重修选课确认API
    const params: RestudySelectParams = {
      xkxxid: currentCourse.value.id,
      optype: 'rebuildSelect',
      glcxkcid: selectedRelatedCourse.value.id,
    }

    await confirmRestudySelect(params)

    uni.showToast({
      title: '重修选课成功',
      icon: 'success',
    })

    // 关闭弹窗并刷新列表
    closeRestudyDialog()
    fetchRebuildCourses()
  } catch (error: any) {
    console.error('重修选课失败:', error)
    uni.showToast({
      title: error.msg || '选课失败',
      icon: 'none',
    })
  } finally {
    submitLoading.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchRebuildCourses()
})
</script>

<template>
  <!-- 搜索框区域 -->
  <view class="bg-white py-2 px-3">
    <view class="flex items-center">
      <!-- 搜索类型选择器 -->
      <view class="search-type-selector mr-2">
        <picker
          :range="searchOptions"
          range-key="label"
          :value="searchOptions.findIndex((item) => item.value === searchType)"
          @change="(e: any) => handleSearchTypeChange(searchOptions[e.detail.value].value)"
        >
          <view
            class="picker-view flex items-center justify-center text-sm py-2 px-3 bg-gray-100 rounded-lg"
          >
            <text>{{ searchOptions.find((item) => item.value === searchType)?.label }}</text>
            <wd-icon name="chevron-down" size="14px" class="ml-1" />
          </view>
        </picker>
      </view>

      <!-- 搜索输入框 -->
      <view class="search-input-container flex-1 flex items-center bg-gray-100 rounded-lg px-3">
        <input
          v-model="searchValue"
          class="search-input flex-1 py-2 text-sm"
          :placeholder="`请输入${searchType === 'courseName' ? '课程名称' : searchType === 'leaderTeacherName' ? '教师姓名' : '教学班'}`"
          confirm-type="search"
          @confirm="handleSearch"
        />
        <wd-icon
          v-if="searchValue"
          name="close-circle"
          size="16px"
          class="text-gray-400 mr-1"
          @click="clearSearch"
        />
        <wd-icon name="search" size="16px" class="text-blue-600" @click="handleSearch" />
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view v-if="loading" class="flex justify-center items-center py-10">
    <wd-loading color="#007aff" />
  </view>

  <!-- 课程列表 -->
  <view v-else class="mt-2 bg-white">
    <view v-if="rebuildCourses.length === 0" class="p-10 text-center text-gray-500">
      <wd-icon name="empty" size="60" color="#d1d1d6" />
      <view class="mt-2">暂无重修课程</view>
    </view>
    <view
      v-for="course in rebuildCourses"
      :key="course.id"
      class="p-4 border-b border-gray-200 last:border-b-0 border-b-solid"
    >
      <view class="flex justify-between items-start mb-2">
        <view class="course-info-container">
          <view class="course-heading">
            <StatusTag
              :text="`【${course.courseCategoryName}】`"
              type="warning"
              size="mini"
              class="course-type-tag"
            />
            <text class="font-medium text-lg course-name">{{ course.courseName }}</text>
          </view>
        </view>
        <!-- 选课按钮 -->
        <view class="flex-shrink-0">
          <ActionButton
            type="primary"
            size="small"
            text="选课"
            @click="openRestudyDialog(course)"
          />
        </view>
      </view>

      <!-- 开课教师 -->
      <view class="grid grid-cols-2 gap-2 text-sm mb-2">
        <view class="flex items-center">
          <wd-icon name="user" class="text-gray-400 mr-1" />
          <text>开课教师: {{ course.leaderTeacherName }}</text>
        </view>
        <view class="flex items-center">
          <wd-icon name="user-circle" class="text-gray-400 mr-1" />
          <text>教学班: {{ course.className }}</text>
        </view>
      </view>

      <!-- 总学时和周学时 -->
      <view class="grid grid-cols-2 gap-2 text-sm mb-2">
        <view class="flex items-center">
          <wd-icon name="hourglass" class="text-gray-400 mr-1" />
          <text>总学时: {{ course.courseTotalHours }}学时</text>
        </view>
        <view class="flex items-center">
          <wd-icon name="time" class="text-gray-400 mr-1" />
          <text>周学时: {{ course.weekHours }}学时</text>
        </view>
      </view>

      <!-- 学分和起始周 -->
      <view class="grid grid-cols-2 gap-2 text-sm mb-2">
        <view class="flex items-center">
          <wd-icon name="books" class="text-gray-400 mr-1" />
          <text>学分: {{ course.creditHour }}</text>
        </view>
        <view class="flex items-center">
          <wd-icon name="calendar" class="text-gray-400 mr-1" />
          <text>起始周: {{ course.startWeek || '暂无' }}</text>
        </view>
      </view>

      <!-- 授课时间 -->
      <view class="grid grid-cols-1 gap-2 text-sm mb-2">
        <view class="flex items-center">
          <wd-icon name="time" class="text-gray-400 mr-1" />
          <text>授课时间: {{ course.teachingInfo || '暂无' }}</text>
        </view>
      </view>

      <!-- 授课场地 -->
      <view class="grid grid-cols-1 gap-2 text-sm mb-2">
        <view class="flex items-center">
          <wd-icon name="location" class="text-gray-400 mr-1" />
          <text>授课场地: {{ course.siteName || '暂无' }}</text>
        </view>
      </view>

      <!-- 限定人数和已选人数 -->
      <view class="grid grid-cols-2 gap-2 text-sm mb-2">
        <view class="flex items-center">
          <wd-icon name="user-circle" class="text-gray-400 mr-1" />
          <text>限定人数: {{ course.maxCount || '不限' }}人</text>
        </view>
        <view class="flex items-center">
          <wd-icon name="user-circle" class="text-gray-400 mr-1" />
          <text>
            已选人数:
            <text
              :class="[
                course.selectedCount > 0
                  ? course.selectedCount >= course.maxCount && course.maxCount > 0
                    ? 'text-red-500 font-medium'
                    : 'text-blue-500 font-medium'
                  : 'text-gray-500',
              ]"
            >
              {{ course.selectedCount }}
            </text>
            人
          </text>
        </view>
      </view>

      <!-- 开放校区 -->
      <view class="grid grid-cols-1 gap-2 text-sm">
        <view class="flex items-center">
          <wd-icon name="location" class="text-gray-400 mr-1" />
          <text>开放校区: {{ course.campusName || '全校' }}</text>
        </view>
      </view>
    </view>

    <!-- 分页组件 -->
    <view v-if="rebuildCourses.length > 0" class="px-4 pb-4">
      <Pagination
        :page="queryParams.page"
        :page-size="queryParams.pageSize"
        :total="total"
        @update:page="handlePageChange"
      />
    </view>
  </view>

  <!-- 重修选课弹窗 -->
  <ConfirmDialog
    ref="restudyDialogRef"
    title="选择重修选课"
    confirm-text="确认选课"
    cancel-text="取消"
    @confirm="submitRestudyApplication"
    @cancel="closeRestudyDialog"
  >
    <template #content>
      <view v-if="currentCourse" class="restudy-content">
        <!-- 课程信息标题 -->
        <view class="course-title">
          {{ currentCourse.leaderTeacherName }} {{ currentCourse.courseName }} (重修教学班{{
            currentCourse.id
          }})
        </view>

        <!-- 重修选课信息 -->
        <view class="info-section">
          <view class="info-title">重修选课信息</view>
          <view class="info-grid">
            <view class="info-item">
              <text class="info-label">总学时</text>
              <text class="info-value">{{ currentCourse.courseTotalHours || '0' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">周学时</text>
              <text class="info-value">{{ currentCourse.weekHours || '0' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">学分</text>
              <text class="info-value">{{ currentCourse.creditHour || '0' }}</text>
            </view>
          </view>
        </view>

        <!-- 关联重修课程选择 -->
        <view class="course-select-section">
          <view class="section-label">关联重修课程</view>
          <OptionSelector
            v-model="selectedCourseIndex"
            :options="courseOptions"
            :selected-value="selectedCourseText"
            placeholder="请选择课程"
            @change="handleCourseOptionChange"
          />
        </view>
      </view>
    </template>
  </ConfirmDialog>
</template>

<style scoped lang="scss">
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.last\:border-b-0:last-child {
  border-bottom-width: 0;
}

.course-info-container {
  flex: 1;
  min-width: 0;
  padding-right: 8rpx;
}

.course-heading {
  display: flex;
  flex-wrap: wrap;
  align-items: baseline;
}

.course-type-tag {
  margin-right: 8rpx;
  margin-bottom: 4rpx;
}

.course-name {
  word-break: break-word;
}

// 搜索框样式
.search-type-selector {
  min-width: 120rpx;
}

.picker-view {
  height: 40rpx;
  line-height: 72rpx;
}

.search-input-container {
  height: 72rpx;
}

.search-input {
  height: 72rpx;
  font-size: 28rpx;
  line-height: 72rpx;
  background-color: transparent;
  border: none;
  outline: none;
}

// 通用样式
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.text-sm {
  font-size: 28rpx;
}

.text-lg {
  font-size: 32rpx;
}

.font-medium {
  font-weight: 500;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.rounded {
  border-radius: 8rpx;
}

.rounded-lg {
  border-radius: 16rpx;
}

.px-2 {
  padding-right: 8rpx;
  padding-left: 8rpx;
}

.px-3 {
  padding-right: 12rpx;
  padding-left: 12rpx;
}

.px-4 {
  padding-right: 16rpx;
  padding-left: 16rpx;
}

.py-1 {
  padding-top: 4rpx;
  padding-bottom: 4rpx;
}

.py-2 {
  padding-top: 8rpx;
  padding-bottom: 8rpx;
}

.py-10 {
  padding-top: 40rpx;
  padding-bottom: 40rpx;
}

.p-4 {
  padding: 16rpx;
}

.p-10 {
  padding: 40rpx;
}

.pb-4 {
  padding-bottom: 16rpx;
}

.mr-1 {
  margin-right: 4rpx;
}

.mr-2 {
  margin-right: 8rpx;
}

.ml-2 {
  margin-left: 8rpx;
}

.mb-2 {
  margin-bottom: 8rpx;
}

.mt-2 {
  margin-top: 8rpx;
}

.gap-2 {
  gap: 8rpx;
}

.bg-white {
  background-color: #ffffff;
}

.bg-gray-100 {
  background-color: #f5f5f5;
}

.bg-gray-200 {
  background-color: #e5e5e5;
}

.text-white {
  color: #ffffff;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-600 {
  color: #4b5563;
}

.text-blue-500 {
  color: #3b82f6;
}

.text-blue-600 {
  color: #2563eb;
}

.text-red-500 {
  color: #ef4444;
}

.text-center {
  text-align: center;
}

.border-b {
  border-bottom-width: 1rpx;
}

.border-gray-200 {
  border-color: #e5e7eb;
}

.border-b-solid {
  border-bottom-style: solid;
}

// 选课按钮样式
.select-course-btn {
  min-width: 120rpx;
  height: 60rpx;
  font-size: 24rpx;
}

// 重修选课弹窗内容样式
.restudy-content {
  padding: 16rpx 0;
}

.course-title {
  margin-bottom: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  text-align: center;
}

.info-section {
  margin-bottom: 24rpx;
}

.info-title {
  margin-bottom: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  padding: 16rpx;
  background: #f9fafb;
  border-radius: 8rpx;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.info-label {
  font-size: 26rpx;
  color: #6b7280;
}

.info-value {
  font-size: 26rpx;
  font-weight: 500;
  color: #1f2937;
}

.course-select-section {
  margin-bottom: 16rpx;
}

.section-label {
  margin-bottom: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
}
</style>
