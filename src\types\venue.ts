/**
 * 授课场地相关类型定义
 */

/**
 * 场地信息查询参数
 */
export interface VenueQuery {
  /** 页码 */
  page: number
  /** 每页大小 */
  pageSize: number
  /** 搜索关键词 */
  searchKeyword?: string
}

/**
 * 场地使用申请查询参数
 */
export interface SiteUseApplyQuery {
  /** 页码 */
  page: number
  /** 每页大小 */
  pageSize: number
  /** 搜索关键词 */
  searchKeyword?: string
  /** 学年 */
  xn?: string
  /** 学期 */
  xq?: string
  /** 申请人 */
  sqr?: string
  /** 使用部门代码 */
  sybmdm?: string
  /** 审批状态 */
  spzt?: number
}

/**
 * 场地信息响应结果
 */
export interface VenueResponse {
  /** 列表数据 */
  items: Venue[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总数 */
  total: number
}

/**
 * 场地使用申请响应结果
 */
export interface SiteUseApplyResponse {
  /** 列表数据 */
  items: SiteUseApply[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总数 */
  total: number
}

/**
 * 场地信息
 */
export interface Venue {
  /** 场地ID */
  id: number
  /** 场地代码 */
  siteCode: string
  /** 场地名称 */
  siteName: string
  /** 场地名称（兼容字段） */
  name?: string
  /** 场地类型 */
  type?: string
  /** 场地类型名称 */
  typeName?: string
  /** 分类名称 */
  categoryName: string
  /** 校区代码 */
  campusCode?: string
  /** 校区名称 */
  campusName: string
  /** 建筑楼代码 */
  buildingCode?: string
  /** 建筑楼名称 */
  buildingName: string
  /** 楼层 */
  floor?: number
  /** 房间号 */
  roomNumber?: string
  /** 容量 */
  capacity?: number
  /** 学生容量 */
  studentCapacity: number
  /** 状态 */
  status?: 'free' | 'occupied' | 'maintenance'
  /** 位置描述 */
  location?: string
  /** 设备信息 */
  equipment?: string
  /** 备注 */
  remark?: string
  /** 创建时间 */
  create_time?: number
  /** 更新时间 */
  update_time?: number
  /** 删除标记 */
  deltag?: number
  /** 操作人员编号 */
  oprybh?: string
}

/**
 * 场地使用申请信息
 */
export interface SiteUseApply {
  /** 申请ID */
  id: number
  /** 学年 */
  xn: string
  /** 学期 */
  xq: string
  /** 授课计划表ID */
  skjhbid: string
  /** 使用状态 */
  syzt: string
  /** 申请人编号 */
  sqr: string
  /** 申请人姓名 */
  sqrxm: string
  /** 申请人电话 */
  sqrdh: string
  /** 使用部门代码 */
  sybmdm: string
  /** 使用部门名称 */
  sybmmc: string
  /** 使用场地代码 */
  sycddm: string
  /** 使用场地名称 */
  sycdmc: string
  /** 申请人类型 */
  sqrlx: number
  /** 申请人类型名称 */
  sqrlxmc: string
  /** 提报时间 */
  tbsj: string
  /** 提报日期 */
  tbrq: string
  /** 审批状态 */
  spzt: number
  /** 附件列表 */
  fjlb: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 备注 */
  remark: string | null
  /** 操作人员编号 */
  oprybh: string
}

/**
 * 场地使用申请详情信息
 */
export interface SiteUseApplyDetail {
  id: number
  jxrwid: number
  zc: number
  skrq: string // 使用日期
  skkssj: string
  skjssj: string
  xqs: number // 星期
  jc: string // 节次
  jcshow: string
  sknl: string // 使用主题
  skfs: string
  skfsmc: string
  zyts: number
  zypgfs: string
  zypgfsmc: string
  fzrszs: string
  syyqsbsl: string
  skcdlx: string
  skcddm: string
  skcdmc: string // 使用场地
  skjs: string
  skjsxm: string // 负责人
  skbj: string
  skbjmc: string
  syrs: number
  remark: string // 备注
  create_time: number
  update_time: number
  deltag: number
  oprybh: string
  ks: number
  xz: number
  glid: number
  skjhzxzt: number
  skjhgzlrdzt: number
  zynr: string
  skjhjsqrzt: number
  skjhjsqrsj: string
  skjhjsqrrybh: string
  skjhjsqrryxm: string
  skjhxsqrzt: number
  skjhxsqrsj: string
  skjhxsqrxsxh: string
  skjhxsqrxsxm: string
  cdsbzt: number
  cdsbsyqk: string
  spzt: number // 状态
  wljxglid: number
  xsfzid: number
  xsfzmc: string
  fjlb: string
  kqzp: string
  ttbksqzt: number
  csqrshzt: number
  zyfjlb: string
  zbptdm: string
  zbptmc: string
  zbptnr: string
  jxptdm: string
  jxptmc: string
  jxptnr: string
  xsskshzt: number
  lb: number
  lx: string
  ssxq: string
  ssxqmc: string
  ssjzl: string
  ssjzlmc: string
}

/**
 * 场地使用申请详情列表请求参数
 */
export interface SiteUseApplyDetailListParams {
  page: number
  pageSize: number
  pid: number
  spzt: number
}

/**
 * 场地使用申请详情列表响应
 */
export interface SiteUseApplyDetailListResponse {
  items: SiteUseApplyDetail[]
  query: Record<string, any>
  total: number
  id: number
}
