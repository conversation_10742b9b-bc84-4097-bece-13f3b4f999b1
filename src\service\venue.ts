import request from '@/utils/request'
import type {
  VenueQuery,
  VenueResponse,
  SiteUseApplyQuery,
  SiteUseApplyResponse,
  SiteUseApplyDetailListParams,
  SiteUseApplyDetailListResponse,
} from '@/types/venue'

/**
 * 获取场地列表
 * @param params 查询参数
 * @returns 场地列表响应
 */
export function getVenueList(params: VenueQuery): Promise<VenueResponse> {
  return request('/site/list', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取场地使用申请列表
 * @param params 查询参数
 * @returns 场地使用申请列表响应
 */
export function getSiteUseApplyList(params: SiteUseApplyQuery): Promise<SiteUseApplyResponse> {
  return request('/siteUseApply/list', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取场地使用申请详情列表
 * @param params 请求参数
 * @returns 场地使用申请详情列表
 */
export function getSiteUseApplyDetailList(
  params: SiteUseApplyDetailListParams,
): Promise<SiteUseApplyDetailListResponse> {
  return request('/siteUseApply/detailList', {
    method: 'POST',
    data: params,
  })
}
