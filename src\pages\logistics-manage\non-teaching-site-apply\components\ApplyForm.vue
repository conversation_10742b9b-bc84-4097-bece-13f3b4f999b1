<script setup lang="ts">
import { ref, computed } from 'vue'
import { useUserStore } from '@/store/user'
import FormContainer from '@/components/common/FormContainer.vue'
import FormItem from '@/components/common/FormItem.vue'
import FormLabel from '@/components/common/FormLabel.vue'
import TextPlaceholder from '@/components/common/TextPlaceholder.vue'
import TextareaInput from '@/components/common/TextareaInput.vue'
import FileUploader from '@/components/common/FileUploader.vue'

// 获取用户信息
const userStore = useUserStore()

// 表单数据
const formData = ref({
  contactPhone: userStore.userInfo.phone || '', // 联系方式，可编辑
  usageTheme: '', // 使用主题
  attachments: [] as Array<{ url: string; name: string }>, // 申请附件
})

// 计算属性：使用部门（从用户信息读取，只读）
const usageDepartment = computed(() => {
  return userStore.userInfo.department || '物联网与人工智能学院'
})

// 计算属性：使用人（从用户信息读取，只读）
const usagePerson = computed(() => {
  return userStore.userInfo.realname || '余新华'
})

// 处理联系方式输入
const handlePhoneInput = (value: string) => {
  formData.value.contactPhone = value
}

// 处理使用主题输入
const handleThemeInput = (value: string) => {
  formData.value.usageTheme = value
}

// 处理附件上传
const handleAttachmentsChange = (files: Array<{ url: string; name: string }>) => {
  formData.value.attachments = files
}

// 暴露表单数据给父组件
defineExpose({
  formData,
})
</script>

<template>
  <FormContainer>
    <!-- 使用部门 -->
    <FormItem>
      <FormLabel text="使用部门" />
      <TextPlaceholder :value="usageDepartment" />
    </FormItem>

    <!-- 使用人 -->
    <FormItem>
      <FormLabel text="使用人" />
      <TextPlaceholder :value="usagePerson" />
    </FormItem>

    <!-- 联系方式 -->
    <FormItem>
      <FormLabel text="联系方式" />
      <TextareaInput
        v-model="formData.contactPhone"
        @update:modelValue="handlePhoneInput"
        placeholder="请输入联系方式"
        :maxlength="20"
        :show-count="true"
        :auto-height="true"
      />
    </FormItem>

    <!-- 使用主题 -->
    <FormItem>
      <FormLabel text="使用主题" />
      <TextareaInput
        v-model="formData.usageTheme"
        @update:modelValue="handleThemeInput"
        placeholder="请输入使用主题"
        :maxlength="200"
        :show-count="true"
        :auto-height="true"
      />
    </FormItem>

    <!-- 申请附件 -->
    <FormItem margin-bottom="none">
      <FormLabel text="申请附件" />
      <FileUploader
        v-model="formData.attachments"
        @update:modelValue="handleAttachmentsChange"
        upload-type="site-apply"
        :show-title="false"
        tip-text="支持jpg、png、pdf、word、excel等文件格式"
        empty-text="暂无申请附件"
        :count="5"
      />
    </FormItem>
  </FormContainer>
</template>

<style lang="scss" scoped>
// 申请表单组件样式
</style>
