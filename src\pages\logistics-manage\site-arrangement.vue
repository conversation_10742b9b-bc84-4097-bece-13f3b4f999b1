<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getSiteUseApplyList } from '@/service/venue'
import type { SiteUseApply, SiteUseApplyQuery } from '@/types/venue'
import StatusTag from '@/components/common/StatusTag.vue'
import ActionButton from '@/components/common/ActionButton.vue'
import Pagination from '@/components/Pagination/index.vue'

// 页面标题
uni.setNavigationBarTitle({
  title: '场地使用申请',
})

// 列表数据
const applyList = ref<SiteUseApply[]>([])
const loading = ref(false)

// 分页参数
const pagination = ref({
  page: 1,
  pageSize: 10,
  total: 0,
})

// 搜索参数
const searchForm = ref({
  searchKeyword: '',
})

/**
 * 获取申请列表数据
 */
const getApplyList = async () => {
  try {
    loading.value = true

    const params: SiteUseApplyQuery = {
      page: pagination.value.page,
      pageSize: pagination.value.pageSize,
      searchKeyword: searchForm.value.searchKeyword || undefined,
    }

    const response = await getSiteUseApplyList(params)

    applyList.value = response.items || []
    pagination.value.total = response.total || 0
  } catch (error) {
    console.error('获取申请列表失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

/**
 * 搜索处理
 */
const handleSearch = () => {
  pagination.value.page = 1
  getApplyList()
}

/**
 * 分页变化处理
 */
const handlePageChange = (page: number) => {
  pagination.value.page = page
  getApplyList()
}

/**
 * 获取审批状态信息
 */
const getApprovalStatusInfo = (status: number) => {
  switch (status) {
    case 0:
      return { text: '待审批', type: 'warning' as const }
    case 1:
      return { text: '已通过', type: 'success' as const }
    case 2:
      return { text: '已拒绝', type: 'danger' as const }
    default:
      return { text: '未知状态', type: 'default' as const }
  }
}

/**
 * 格式化日期
 */
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return dateStr.split(' ')[0] // 只显示日期部分
}

/**
 * 格式化时间
 */
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '-'
  return dateStr
}

/**
 * 计算申请场地数量
 */
const getVenueCount = (venueNames: string) => {
  if (!venueNames) return 0
  return venueNames.split(',').length
}

/**
 * 查看详情
 */
const handleView = (item: SiteUseApply) => {
  // TODO: 跳转到详情页面
  console.log('查看详情:', item)
  uni.showToast({
    title: '查看详情功能待实现',
    icon: 'none',
  })
}

/**
 * 撤销申请
 */
const handleRevoke = (item: SiteUseApply) => {
  // TODO: 实现撤销申请功能
  console.log('撤销申请:', item)
  uni.showModal({
    title: '确认撤销',
    content: '确定要撤销此申请吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '撤销申请功能待实现',
          icon: 'none',
        })
      }
    },
  })
}

/**
 * 跳转到非教学场地使用申请页面
 */
const navigateToNonTeachingSiteApply = () => {
  uni.navigateTo({
    url: '/pages/logistics-manage/non-teaching-site-apply/index',
  })
}

// 页面加载时获取数据
onMounted(() => {
  getApplyList()
})
</script>

<template>
  <view class="site-arrangement-page">
    <!-- 搜索区域 -->
    <view class="search-section">
      <view class="search-box">
        <input
          v-model="searchForm.searchKeyword"
          class="search-input"
          placeholder="请输入申请人姓名或部门名称"
          @confirm="handleSearch"
        />
        <button class="search-btn" @click="handleSearch">搜索</button>
      </view>
    </view>

    <!-- 快捷操作区域 -->
    <view class="quick-actions-section">
      <ActionButton
        type="primary"
        size="normal"
        text="非教学场地使用申请"
        @click="navigateToNonTeachingSiteApply"
      />
    </view>

    <!-- 列表区域 -->
    <view class="list-section">
      <view v-if="loading" class="loading-container">
        <text>加载中...</text>
      </view>

      <view v-else-if="applyList.length === 0" class="empty-container">
        <text>暂无数据</text>
      </view>

      <view v-else class="apply-list">
        <view v-for="(item, index) in applyList" :key="item.id" class="apply-item">
          <!-- 序号和申请编号 -->
          <view class="item-header">
            <view class="serial-number">
              {{ (pagination.page - 1) * pagination.pageSize + index + 1 }}
            </view>
            <view class="apply-id">申请编号: {{ item.id }}</view>
          </view>

          <!-- 基本信息 -->
          <view class="item-content">
            <view class="info-row">
              <text class="label">使用部门:</text>
              <text class="value">{{ item.sybmmc }}</text>
            </view>

            <view class="info-row">
              <text class="label">申请场地:</text>
              <text class="value">{{ item.sycdmc }}</text>
            </view>

            <view class="info-row">
              <text class="label">申请人:</text>
              <text class="value">{{ item.sqrxm }}</text>
            </view>

            <view class="info-row">
              <text class="label">联系方式:</text>
              <text class="value">{{ item.sqrdh }}</text>
            </view>

            <view class="info-row">
              <text class="label">发起申请日期:</text>
              <text class="value">{{ formatDate(item.tbrq) }}</text>
            </view>

            <view class="info-row">
              <text class="label">申请表更新时间:</text>
              <text class="value">{{ formatDateTime(item.tbsj) }}</text>
            </view>

            <view class="info-row">
              <text class="label">申请场地数量:</text>
              <text class="value">{{ getVenueCount(item.sycdmc) }}个</text>
            </view>

            <view class="info-row">
              <text class="label">审批状态:</text>
              <StatusTag
                :text="getApprovalStatusInfo(item.spzt).text"
                :type="getApprovalStatusInfo(item.spzt).type"
                size="small"
              />
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="item-actions">
            <ActionButton type="success" size="small" text="查看" @click="handleView(item)" />
            <ActionButton
              type="danger"
              size="small"
              text="撤销申请"
              :disabled="item.spzt !== 0"
              @click="handleRevoke(item)"
            />
          </view>
        </view>
      </view>
    </view>

    <!-- 分页组件 -->
    <view v-if="applyList.length > 0" class="pagination-section">
      <Pagination
        :total="pagination.total"
        :page="pagination.page"
        :page-size="pagination.pageSize"
        @update:page="handlePageChange"
      />
    </view>
  </view>
</template>

<style scoped lang="scss">
.site-arrangement-page {
  min-height: 100vh;
  padding: 20rpx;
  background-color: #f5f5f5;
}

// 搜索区域
.search-section {
  padding: 24rpx;
  margin-bottom: 20rpx;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.search-box {
  display: flex;
  gap: 20rpx;
  align-items: center;
}

.search-input {
  flex: 1;
  height: 80rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  background-color: #fafafa;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;

  &:focus {
    background-color: white;
    border-color: #007aff;
  }
}

.search-btn {
  height: 80rpx;
  padding: 0 32rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: white;
  background: linear-gradient(135deg, #007aff, #5ac8fa);
  border: none;
  border-radius: 8rpx;

  &:active {
    opacity: 0.8;
  }
}

// 快捷操作区域
.quick-actions-section {
  padding: 24rpx;
  margin-bottom: 20rpx;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

// 列表区域
.list-section {
  flex: 1;
}

.loading-container,
.empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  font-size: 28rpx;
  color: #999;
  background: white;
  border-radius: 12rpx;
}

.apply-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.apply-item {
  padding: 24rpx;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 16rpx;
  margin-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.serial-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  font-size: 24rpx;
  font-weight: bold;
  color: white;
  background: linear-gradient(135deg, #007aff, #5ac8fa);
  border-radius: 50%;
}

.apply-id {
  font-size: 26rpx;
  font-weight: 500;
  color: #666;
}

.item-content {
  margin-bottom: 24rpx;
}

.info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  flex-shrink: 0;
  width: 200rpx;
  font-size: 26rpx;
  font-weight: 500;
  color: #666;
}

.value {
  flex: 1;
  font-size: 26rpx;
  line-height: 1.4;
  color: #333;
  word-break: break-all;
}

.item-actions {
  display: flex;
  gap: 20rpx;
  padding-top: 16rpx;
  border-top: 2rpx solid #f0f0f0;
}

// 分页区域
.pagination-section {
  padding: 24rpx;
  margin-top: 40rpx;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
</style>
